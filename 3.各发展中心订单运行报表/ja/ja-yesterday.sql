-- 各发展中心昨日结案库存报表 - 按结案天数区间统计
-- 根据生产结案天数yjaDays划分区间：<30天、30-90天、90-180天、>180天
-- 直接使用dbo.ODS_T5_vcpkc_cczs_xsfh表的Dept_Name和jarq字段
SELECT
    -- 建材发展中心数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '建材业务服务中心' AND yjaDays < 30
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 建材发展中心_昨天_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '建材业务服务中心' AND yjaDays >= 30 AND yjaDays < 90
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 建材发展中心_昨天_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '建材业务服务中心' AND yjaDays >= 90 AND yjaDays < 180
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 建材发展中心_昨天_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '建材业务服务中心' AND yjaDays >= 180
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 建材发展中心_昨天_结案库存_大于180天,
            
    -- 海外事业发展中心数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays < 30
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 海外事业发展中心_昨天_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays >= 30 AND yjaDays < 90
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 海外事业发展中心_昨天_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays >= 90 AND yjaDays < 180
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 海外事业发展中心_昨天_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays >= 180
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 海外事业发展中心_昨天_结案库存_大于180天,

    -- 新材发展中心数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '新材料业务部' AND yjaDays < 30
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 新材发展中心_昨天_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '新材料业务部' AND yjaDays >= 30 AND yjaDays < 90
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 新材发展中心_昨天_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '新材料业务部' AND yjaDays >= 90 AND yjaDays < 180
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 新材发展中心_昨天_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '新材料业务部' AND yjaDays >= 180
            AND CONVERT(date, jarq) = CONVERT(date, DATEADD(day, -1, GETDATE()))), 0) AS 新材发展中心_昨天_结案库存_大于180天
