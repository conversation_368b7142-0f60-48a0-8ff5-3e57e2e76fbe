-- 各客服中心本季度库存报表 - 按库存天数区间统计
-- 使用主表jarq获取日期，通过关联dbo.ODS_T5_HJLY_VXSDD_LIST表获取scAddr
SELECT
    -- 一中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr = '一中心'
            AND DATEPART(quarter, a.jarq) = DATEPART(quarter, GETDATE()) AND YEAR(a.jarq) = YEAR(GETDATE())), 0) AS 一中心_本季度_库存,

    -- 二中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr = '二中心'
            AND DATEPART(quarter, a.jarq) = DATEPART(quarter, GETDATE()) AND YEAR(a.jarq) = YEAR(GETDATE())), 0) AS 二中心_本季度_库存,

    -- 新材中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr = '新材中心'
            AND DATEPART(quarter, a.jarq) = DATEPART(quarter, GETDATE()) AND YEAR(a.jarq) = YEAR(GETDATE())), 0) AS 新材中心_本季度_库存,

    -- 其他中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS a
            INNER JOIN dbo.ODS_T5_HJLY_VXSDD_LIST b ON a.dzid = b.dzid
            WHERE b.scAddr NOT IN ('一中心', '二中心', '新材中心')
            AND DATEPART(quarter, a.jarq) = DATEPART(quarter, GETDATE()) AND YEAR(a.jarq) = YEAR(GETDATE())), 0) AS 其他_本季度_库存