
-- 各客服中心本周发货报表 - 销售单号总量统计
-- 通过关联dbo.ODS_T5_vscdd_dzbh表获取scAddr，使用dzid关联xsdzid
SELECT
    -- 一中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr = '一中心'
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())), 0) AS 一中心_本周_发货量,

    -- 二中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr = '二中心'
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())), 0) AS 二中心_本周_发货量,

    -- 新材中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr = '新材中心'
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())), 0) AS 新材中心_本周_发货量,

    -- 其他中心数据
    ISNULL((SELECT COUNT(DISTINCT a.dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST a
            INNER JOIN dbo.ODS_T5_vscdd_dzbh b ON a.dzid = b.xsdzid
            WHERE b.scAddr NOT IN ('一中心', '二中心', '新材中心')
            AND CONVERT(date, a.dzrq) >= CONVERT(date, DATEADD(week, DATEDIFF(week, 0, GETDATE()), 0))
            AND CONVERT(date, a.dzrq) <= CONVERT(date, GETDATE())), 0) AS 其他_本周_发货量

