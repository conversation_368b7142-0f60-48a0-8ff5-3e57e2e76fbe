-- 市场各区域本月发货报表 - 销售单号总量统计
-- 统计dbo.ODS_T5_HJLY_VXSJS_LIST表中各区域本月的销售单号总量
SELECT
    -- 东部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST
            WHERE Dept_Name = '东部大区' 
            AND CONVERT(date, dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, dzrq) <= CONVERT(date, GETDATE())), 0) AS 东部大区_本月_发货量,

    -- 西部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST
            WHERE Dept_Name = '西部大区' 
            AND CONVERT(date, dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, dzrq) <= CONVERT(date, GETDATE())), 0) AS 西部大区_本月_发货量,

    -- 南部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST
            WHERE Dept_Name = '南部大区' 
            AND CONVERT(date, dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, dzrq) <= CONVERT(date, GETDATE())), 0) AS 南部大区_本月_发货量,

    -- 海外事业发展数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST
            WHERE Dept_Name = '海外事业发展' 
            AND CONVERT(date, dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, dzrq) <= CONVERT(date, GETDATE())), 0) AS 海外事业发展_本月_发货量,

    -- 新材各区域及部门汇总数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VXSJS_LIST
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')
            AND CONVERT(date, dzrq) >= CONVERT(date, DATEADD(month, DATEDIFF(month, 0, GETDATE()), 0))
            AND CONVERT(date, dzrq) <= CONVERT(date, GETDATE())), 0) AS 新材各区域及部门_本月_发货量

