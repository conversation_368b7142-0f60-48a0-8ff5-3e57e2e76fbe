-- 市场各区域本年度结案库存报表 - 按结案天数区间统计
-- 根据生产结案天数yjaDays划分区间：<30天、30-90天、90-180天、>180天
-- 直接使用dbo.ODS_T5_vcpkc_cczs_xsfh表的Dept_Name和jarq字段
SELECT
    -- 东部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '东部大区' AND yjaDays < 30
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 东部大区_本年度_结案库存_小于30天,
            
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '东部大区' AND yjaDays >= 30 AND yjaDays < 90
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 东部大区_本年度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '东部大区' AND yjaDays >= 90 AND yjaDays < 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 东部大区_本年度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '东部大区' AND yjaDays >= 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 东部大区_本年度_结案库存_大于180天,
            
    -- 西部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '西部大区' AND yjaDays < 30
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 西部大区_本年度_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '西部大区' AND yjaDays >= 30 AND yjaDays < 90
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 西部大区_本年度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '西部大区' AND yjaDays >= 90 AND yjaDays < 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 西部大区_本年度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '西部大区' AND yjaDays >= 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 西部大区_本年度_结案库存_大于180天,

    -- 南部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '南部大区' AND yjaDays < 30
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 南部大区_本年度_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '南部大区' AND yjaDays >= 30 AND yjaDays < 90
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 南部大区_本年度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '南部大区' AND yjaDays >= 90 AND yjaDays < 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 南部大区_本年度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '南部大区' AND yjaDays >= 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 南部大区_本年度_结案库存_大于180天,

    -- 海外事业发展数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays < 30
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 海外事业发展_本年度_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays >= 30 AND yjaDays < 90
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 海外事业发展_本年度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays >= 90 AND yjaDays < 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 海外事业发展_本年度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name = '海外事业发展' AND yjaDays >= 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 海外事业发展_本年度_结案库存_大于180天,

    -- 新材各区域及部门汇总数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') AND yjaDays < 30
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本年度_结案库存_小于30天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') AND yjaDays >= 30 AND yjaDays < 90
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本年度_结案库存_30至90天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') AND yjaDays >= 90 AND yjaDays < 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本年度_结案库存_90至180天,

    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_vcpkc_cczs_xsfh
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域') AND yjaDays >= 180
            AND YEAR(jarq) = YEAR(GETDATE())), 0) AS 新材各区域及部门_本年度_结案库存_大于180天
